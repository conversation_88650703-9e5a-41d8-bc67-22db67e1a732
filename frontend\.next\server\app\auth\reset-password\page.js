/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/reset-password/page";
exports.ids = ["app/auth/reset-password/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Freset-password%2Fpage&page=%2Fauth%2Freset-password%2Fpage&appPaths=%2Fauth%2Freset-password%2Fpage&pagePath=private-next-app-dir%2Fauth%2Freset-password%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Freset-password%2Fpage&page=%2Fauth%2Freset-password%2Fpage&appPaths=%2Fauth%2Freset-password%2Fpage&pagePath=private-next-app-dir%2Fauth%2Freset-password%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'reset-password',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/reset-password/page.tsx */ \"(rsc)/./src/app/auth/reset-password/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/reset-password/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/reset-password/page\",\n        pathname: \"/auth/reset-password\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhdXRoJTJGcmVzZXQtcGFzc3dvcmQlMkZwYWdlJnBhZ2U9JTJGYXV0aCUyRnJlc2V0LXBhc3N3b3JkJTJGcGFnZSZhcHBQYXRocz0lMkZhdXRoJTJGcmVzZXQtcGFzc3dvcmQlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXV0aCUyRnJlc2V0LXBhc3N3b3JkJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q2d1cHRhJTVDRGVza3RvcCU1Q2ludm9OZXN0JTVDZnJvbnRlbmQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2d1cHRhJTVDRGVza3RvcCU1Q2ludm9OZXN0JTVDZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLHdMQUE0SDtBQUNuSjtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUF3RztBQUNqSSxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvPzdhNjAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnYXV0aCcsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ3Jlc2V0LXBhc3N3b3JkJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ3VwdGFcXFxcRGVza3RvcFxcXFxpbnZvTmVzdFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxccmVzZXQtcGFzc3dvcmRcXFxccGFnZS50c3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcZ3VwdGFcXFxcRGVza3RvcFxcXFxpbnZvTmVzdFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxccmVzZXQtcGFzc3dvcmRcXFxccGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZ3VwdGFcXFxcRGVza3RvcFxcXFxpbnZvTmVzdFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcZ3VwdGFcXFxcRGVza3RvcFxcXFxpbnZvTmVzdFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkM6XFxcXFVzZXJzXFxcXGd1cHRhXFxcXERlc2t0b3BcXFxcaW52b05lc3RcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxhdXRoXFxcXHJlc2V0LXBhc3N3b3JkXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXV0aC9yZXNldC1wYXNzd29yZC9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL2F1dGgvcmVzZXQtcGFzc3dvcmQvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXV0aC9yZXNldC1wYXNzd29yZFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Freset-password%2Fpage&page=%2Fauth%2Freset-password%2Fpage&appPaths=%2Fauth%2Freset-password%2Fpage&pagePath=private-next-app-dir%2Fauth%2Freset-password%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CRecaptchaProvider.tsx%22%2C%22ids%22%3A%5B%22RecaptchaProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CRecaptchaProvider.tsx%22%2C%22ids%22%3A%5B%22RecaptchaProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/RecaptchaProvider.tsx */ \"(ssr)/./src/components/auth/RecaptchaProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CRecaptchaProvider.tsx%22%2C%22ids%22%3A%5B%22RecaptchaProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Creset-password%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Creset-password%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/reset-password/page.tsx */ \"(ssr)/./src/app/auth/reset-password/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2d1cHRhJTVDJTVDRGVza3RvcCU1QyU1Q2ludm9OZXN0JTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhdXRoJTVDJTVDcmVzZXQtcGFzc3dvcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQTRIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvP2ZlNjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxndXB0YVxcXFxEZXNrdG9wXFxcXGludm9OZXN0XFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxyZXNldC1wYXNzd29yZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Creset-password%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/reset-password/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/auth/reset-password/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResetPasswordPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/auth/RecaptchaProvider */ \"(ssr)/./src/components/auth/RecaptchaProvider.tsx\");\n/* harmony import */ var _components_auth_OTPInput__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/auth/OTPInput */ \"(ssr)/./src/components/auth/OTPInput.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction ResetPasswordPage() {\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"otp\");\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [otp, setOtp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newPassword, setNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [resetToken, setResetToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { executeRecaptcha } = (0,_components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_5__.useRecaptchaContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get email from URL params if coming from forgot password page\n        const emailParam = searchParams.get(\"email\");\n        if (emailParam) {\n            setEmail(emailParam);\n        }\n    }, [\n        searchParams\n    ]);\n    const handleOTPSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            const apiUrl = `${\"http://localhost:5000\" || 0}/api/otp/verify-password-reset`;\n            const response = await fetch(apiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    otp\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setResetToken(data.data.resetToken);\n                setStep(\"password\");\n                setSuccess(\"OTP verified! Please enter your new password.\");\n            } else {\n                setError(data.message || \"Invalid OTP. Please try again.\");\n            }\n        } catch (err) {\n            console.error(\"OTP verification error:\", err);\n            setError(\"An error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        // Validate passwords match\n        if (newPassword !== confirmPassword) {\n            setError(\"Passwords do not match.\");\n            setLoading(false);\n            return;\n        }\n        // Validate password strength\n        if (newPassword.length < 8) {\n            setError(\"Password must be at least 8 characters long.\");\n            setLoading(false);\n            return;\n        }\n        try {\n            const apiUrl = `${\"http://localhost:5000\" || 0}/api/auth/reset-password`;\n            const response = await fetch(apiUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    resetToken,\n                    newPassword\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setSuccess(\"Password reset successfully! Redirecting to login...\");\n                setTimeout(()=>{\n                    router.push(\"/login?message=Password reset successfully. Please login with your new password.\");\n                }, 2000);\n            } else {\n                setError(data.message || \"Failed to reset password. Please try again.\");\n            }\n        } catch (err) {\n            console.error(\"Password reset error:\", err);\n            setError(\"An error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (success && step === \"password\" && !error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex justify-center items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-white rounded-lg flex items-center justify-center border border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: \"/invologo.png\",\n                                            alt: \"InvoNest Logo\",\n                                            width: 32,\n                                            height: 32,\n                                            className: \"object-contain w-full h-full\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-indigo-600\",\n                                    children: \"InvoNest\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8 text-green-600\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M5 13l4 4L19 7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                    children: \"Password Reset Successful!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: success\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/\",\n                            className: \"flex justify-center items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-white rounded-lg flex items-center justify-center border border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: \"/invologo.png\",\n                                            alt: \"InvoNest Logo\",\n                                            width: 32,\n                                            height: 32,\n                                            className: \"object-contain w-full h-full\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-indigo-600\",\n                                    children: \"InvoNest\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                            children: step === \"otp\" ? \"Verify OTP\" : \"Set New Password\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: step === \"otp\" ? \"Enter the 6-digit OTP sent to your email address.\" : \"Enter your new password to complete the reset process.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this),\n                step === \"otp\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleOTPSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"email\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"email\",\n                                    name: \"email\",\n                                    type: \"email\",\n                                    required: true,\n                                    className: \"appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                    placeholder: \"Enter your email address\",\n                                    value: email,\n                                    onChange: (e)=>setEmail(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"otp\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"OTP Code\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_OTPInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    value: otp,\n                                    onChange: setOtp,\n                                    length: 6,\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 15\n                        }, this),\n                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading || !otp || otp.length !== 6,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Verifying...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 19\n                                }, this) : \"Verify OTP\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/auth/forgot-password\",\n                                className: \"text-sm text-indigo-600 hover:text-indigo-500\",\n                                children: \"Didn't receive OTP? Send again\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_5__.RecaptchaBadge, {\n                            className: \"text-center\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handlePasswordSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"newPassword\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"newPassword\",\n                                            name: \"newPassword\",\n                                            type: showPassword ? \"text\" : \"password\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                            placeholder: \"Enter new password\",\n                                            value: newPassword,\n                                            onChange: (e)=>setNewPassword(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-gray-400\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-gray-400\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmPassword\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm New Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"confirmPassword\",\n                                            name: \"confirmPassword\",\n                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                            required: true,\n                                            className: \"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\",\n                                            placeholder: \"Confirm new password\",\n                                            value: confirmPassword,\n                                            onChange: (e)=>setConfirmPassword(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-gray-400\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-gray-400\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading || !newPassword || !confirmPassword,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    className: \"opacity-25\",\n                                                    cx: \"12\",\n                                                    cy: \"12\",\n                                                    r: \"10\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    className: \"opacity-75\",\n                                                    fill: \"currentColor\",\n                                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 21\n                                        }, this),\n                                        \"Resetting...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 19\n                                }, this) : \"Reset Password\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_5__.RecaptchaBadge, {\n                            className: \"text-center\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/login\",\n                        className: \"text-sm text-indigo-600 hover:text-indigo-500\",\n                        children: \"Back to Sign In\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n            lineNumber: 161,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/reset-password/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/OTPInput.tsx":
/*!******************************************!*\
  !*** ./src/components/auth/OTPInput.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OTPInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction OTPInput({ length = 6, onComplete, loading = false, error = \"\", className = \"\" }) {\n    const [otp, setOtp] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Array(length).fill(\"\"));\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const inputRefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    const submittedOtpRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Focus first input on mount\n        if (inputRefs.current[0]) {\n            inputRefs.current[0].focus();\n        }\n    }, []);\n    const handleComplete = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((otpValue)=>{\n        if (submittedOtpRef.current === otpValue) {\n            return; // Already submitted this OTP\n        }\n        submittedOtpRef.current = otpValue;\n        onComplete(otpValue);\n    }, [\n        onComplete\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Call onComplete when OTP is fully entered\n        const otpValue = otp.join(\"\");\n        if (otpValue.length === length && !loading) {\n            handleComplete(otpValue);\n        }\n    }, [\n        otp,\n        length,\n        handleComplete,\n        loading\n    ]);\n    // Reset submitted OTP when error changes (indicating a failed attempt)\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (error) {\n            submittedOtpRef.current = \"\";\n        }\n    }, [\n        error\n    ]);\n    const handleChange = (value, index)=>{\n        // Only allow numeric input\n        if (!/^\\d*$/.test(value)) return;\n        const newOtp = [\n            ...otp\n        ];\n        // Handle paste\n        if (value.length > 1) {\n            const pastedData = value.slice(0, length);\n            for(let i = 0; i < length; i++){\n                newOtp[i] = pastedData[i] || \"\";\n            }\n            setOtp(newOtp);\n            // Focus last filled input or next empty input\n            const lastFilledIndex = Math.min(pastedData.length - 1, length - 1);\n            const nextIndex = pastedData.length < length ? pastedData.length : length - 1;\n            setActiveIndex(nextIndex);\n            if (inputRefs.current[nextIndex]) {\n                inputRefs.current[nextIndex]?.focus();\n            }\n            return;\n        }\n        // Handle single character input\n        newOtp[index] = value;\n        setOtp(newOtp);\n        // Move to next input if value is entered\n        if (value && index < length - 1) {\n            setActiveIndex(index + 1);\n            inputRefs.current[index + 1]?.focus();\n        }\n    };\n    const handleKeyDown = (e, index)=>{\n        if (e.key === \"Backspace\") {\n            e.preventDefault();\n            const newOtp = [\n                ...otp\n            ];\n            if (otp[index]) {\n                // Clear current input\n                newOtp[index] = \"\";\n                setOtp(newOtp);\n            } else if (index > 0) {\n                // Move to previous input and clear it\n                newOtp[index - 1] = \"\";\n                setOtp(newOtp);\n                setActiveIndex(index - 1);\n                inputRefs.current[index - 1]?.focus();\n            }\n        } else if (e.key === \"ArrowLeft\" && index > 0) {\n            setActiveIndex(index - 1);\n            inputRefs.current[index - 1]?.focus();\n        } else if (e.key === \"ArrowRight\" && index < length - 1) {\n            setActiveIndex(index + 1);\n            inputRefs.current[index + 1]?.focus();\n        } else if (e.key === \"Enter\") {\n            e.preventDefault();\n            const otpValue = otp.join(\"\");\n            if (otpValue.length === length) {\n                handleComplete(otpValue);\n            }\n        }\n    };\n    const handleFocus = (index)=>{\n        setActiveIndex(index);\n    };\n    const handlePaste = (e)=>{\n        e.preventDefault();\n        const pastedData = e.clipboardData.getData(\"text\").replace(/\\D/g, \"\");\n        if (pastedData) {\n            const newOtp = [\n                ...otp\n            ];\n            for(let i = 0; i < length; i++){\n                newOtp[i] = pastedData[i] || \"\";\n            }\n            setOtp(newOtp);\n            // Focus appropriate input\n            const nextIndex = Math.min(pastedData.length, length - 1);\n            setActiveIndex(nextIndex);\n            inputRefs.current[nextIndex]?.focus();\n        }\n    };\n    const clearOTP = ()=>{\n        setOtp(new Array(length).fill(\"\"));\n        setActiveIndex(0);\n        submittedOtpRef.current = \"\"; // Reset submitted OTP\n        inputRefs.current[0]?.focus();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-67c32b5eb07aac71\" + \" \" + `otp-input-container ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-67c32b5eb07aac71\" + \" \" + \"flex justify-center space-x-3 mb-4\",\n                children: otp.map((digit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: (ref)=>inputRefs.current[index] = ref,\n                        type: \"text\",\n                        inputMode: \"numeric\",\n                        pattern: \"\\\\d*\",\n                        maxLength: 1,\n                        value: digit,\n                        onChange: (e)=>handleChange(e.target.value, index),\n                        onKeyDown: (e)=>handleKeyDown(e, index),\n                        onFocus: ()=>handleFocus(index),\n                        onPaste: handlePaste,\n                        disabled: loading,\n                        \"aria-label\": `OTP digit ${index + 1}`,\n                        className: \"jsx-67c32b5eb07aac71\" + \" \" + `\n              w-12 h-12 text-center text-xl font-bold border-2 rounded-lg\n              transition-all duration-200 focus:outline-none text-gray-900 bg-white\n              ${digit ? \"border-indigo-500 bg-indigo-50\" : \"border-gray-300\"}\n              ${activeIndex === index ? \"ring-2 ring-indigo-500 ring-opacity-50\" : \"\"}\n              ${error ? \"border-red-500\" : \"\"}\n              ${loading ? \"opacity-50 cursor-not-allowed\" : \"hover:border-indigo-400\"}\n              disabled:opacity-50 disabled:cursor-not-allowed\n            `\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-67c32b5eb07aac71\" + \" \" + \"text-red-600 text-sm text-center mb-4 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        className: \"jsx-67c32b5eb07aac71\" + \" \" + \"w-4 h-4 mr-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\",\n                            className: \"jsx-67c32b5eb07aac71\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-67c32b5eb07aac71\" + \" \" + \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-67c32b5eb07aac71\" + \" \" + \"inline-flex items-center text-indigo-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-67c32b5eb07aac71\" + \" \" + \"animate-spin h-4 w-4 border-2 border-indigo-600 border-t-transparent rounded-full mr-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"jsx-67c32b5eb07aac71\" + \" \" + \"text-sm\",\n                            children: \"Verifying...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-67c32b5eb07aac71\" + \" \" + \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    onClick: clearOTP,\n                    disabled: loading || otp.every((digit)=>!digit),\n                    className: \"jsx-67c32b5eb07aac71\" + \" \" + \"text-sm text-gray-800 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                    children: \"Clear\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"67c32b5eb07aac71\",\n                children: \".otp-input-container.jsx-67c32b5eb07aac71 input.jsx-67c32b5eb07aac71::-webkit-outer-spin-button,.otp-input-container.jsx-67c32b5eb07aac71 input.jsx-67c32b5eb07aac71::-webkit-inner-spin-button{-webkit-appearance:none;margin:0}.otp-input-container.jsx-67c32b5eb07aac71 input[type=number].jsx-67c32b5eb07aac71{-moz-appearance:textfield}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\OTPInput.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/OTPInput.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/RecaptchaProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/auth/RecaptchaProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecaptchaBadge: () => (/* binding */ RecaptchaBadge),\n/* harmony export */   RecaptchaProvider: () => (/* binding */ RecaptchaProvider),\n/* harmony export */   useRecaptchaContext: () => (/* binding */ useRecaptchaContext),\n/* harmony export */   withRecaptcha: () => (/* binding */ withRecaptcha)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../hooks/useRecaptcha */ \"(ssr)/./src/hooks/useRecaptcha.ts\");\n/* __next_internal_client_entry_do_not_use__ RecaptchaProvider,useRecaptchaContext,withRecaptcha,RecaptchaBadge auto */ \n\n\n\nconst RecaptchaContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Define pages where reCAPTCHA should be enabled\nconst RECAPTCHA_ENABLED_PAGES = [\n    \"/login\",\n    \"/register\",\n    \"/auth/forgot-password\",\n    \"/auth/reset-password\",\n    \"/login-otp\"\n];\nconst RecaptchaProvider = ({ children })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isRecaptchaEnabled = RECAPTCHA_ENABLED_PAGES.includes(pathname);\n    const siteKey = (0,_hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__.getRecaptchaSiteKey)();\n    // Only initialize reCAPTCHA on enabled pages\n    const { isLoaded, isLoading, executeRecaptcha: baseExecuteRecaptcha } = (0,_hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_3__.useRecaptcha)({\n        siteKey: isRecaptchaEnabled ? siteKey : \"\",\n        action: \"init\"\n    });\n    const executeRecaptcha = async (action)=>{\n        // If reCAPTCHA is not enabled for this page, return null\n        if (!isRecaptchaEnabled) {\n            console.warn(\"reCAPTCHA is not enabled for this page\");\n            return null;\n        }\n        if (!isLoaded || !window.grecaptcha) {\n            console.warn(\"reCAPTCHA not loaded yet\");\n            return null;\n        }\n        try {\n            const token = await window.grecaptcha.execute(siteKey, {\n                action\n            });\n            return token;\n        } catch (error) {\n            console.error(\"reCAPTCHA execution failed:\", error);\n            return null;\n        }\n    };\n    const value = {\n        isLoaded: isRecaptchaEnabled ? isLoaded : false,\n        isLoading: isRecaptchaEnabled ? isLoading : false,\n        executeRecaptcha,\n        isRecaptchaEnabled\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecaptchaContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\nconst useRecaptchaContext = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(RecaptchaContext);\n    if (context === undefined) {\n        throw new Error(\"useRecaptchaContext must be used within a RecaptchaProvider\");\n    }\n    return context;\n};\n// Higher-order component to wrap forms with reCAPTCHA\nconst withRecaptcha = (Component)=>{\n    return (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecaptchaProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n            lineNumber: 89,\n            columnNumber: 5\n        }, undefined);\n};\n// Badge component to show reCAPTCHA branding (required by Google)\nconst RecaptchaBadge = ({ className = \"\" })=>{\n    const { isRecaptchaEnabled } = useRecaptchaContext();\n    // Only show the badge if reCAPTCHA is enabled for the current page\n    if (!isRecaptchaEnabled) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `text-xs text-gray-500 mt-4 ${className}`,\n        children: [\n            \"This site is protected by reCAPTCHA and the Google\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"https://policies.google.com/privacy\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"text-blue-600 hover:text-blue-800 underline\",\n                children: \"Privacy Policy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"and\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"https://policies.google.com/terms\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"text-blue-600 hover:text-blue-800 underline\",\n                children: \"Terms of Service\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"apply.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/RecaptchaProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,useRequireAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            try {\n                if ((0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.isAuthenticated)()) {\n                    // Try to get user from localStorage first\n                    const cachedUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                    if (cachedUser) {\n                        setUser(cachedUser);\n                    }\n                    // Then refresh from server\n                    const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n                    if (freshUser) {\n                        setUser(freshUser);\n                    } else {\n                        // Token might be expired, clear auth state\n                        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                        setUser(null);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    const login = async (data, remember = false)=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.login)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, remember);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during login.\"\n            };\n        }\n    };\n    const register = async (data)=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.register)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, true);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Register error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during registration.\"\n            };\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            router.push(\"/login\");\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        setUser(updatedUser);\n        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(updatedUser);\n    };\n    const refreshUser = async ()=>{\n        try {\n            const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n            if (freshUser) {\n                setUser(freshUser);\n            }\n        } catch (error) {\n            console.error(\"Refresh user error:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        isLoggedIn: !!user,\n        login,\n        register,\n        logout,\n        updateUser,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n// Higher-order component for protected routes\nconst withAuth = (WrappedComponent)=>{\n    const AuthenticatedComponent = (props)=>{\n        const { isLoggedIn, loading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading && !isLoggedIn) {\n                router.push(\"/login\");\n            }\n        }, [\n            isLoggedIn,\n            loading,\n            router\n        ]);\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (!isLoggedIn) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 195,\n            columnNumber: 12\n        }, undefined);\n    };\n    AuthenticatedComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;\n    return AuthenticatedComponent;\n};\n// Hook for protected routes\nconst useRequireAuth = ()=>{\n    const { isLoggedIn, loading } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isLoggedIn) {\n            router.push(\"/login\");\n        }\n    }, [\n        isLoggedIn,\n        loading,\n        router\n    ]);\n    return {\n        isLoggedIn,\n        loading\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useRecaptcha.ts":
/*!***********************************!*\
  !*** ./src/hooks/useRecaptcha.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RECAPTCHA_ACTIONS: () => (/* binding */ RECAPTCHA_ACTIONS),\n/* harmony export */   getRecaptchaSiteKey: () => (/* binding */ getRecaptchaSiteKey),\n/* harmony export */   useRecaptcha: () => (/* binding */ useRecaptcha)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useRecaptcha,getRecaptchaSiteKey,RECAPTCHA_ACTIONS auto */ \nconst useRecaptcha = (config)=>{\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // If no site key is provided, don't load reCAPTCHA\n        if (!config.siteKey) {\n            setIsLoaded(false);\n            return;\n        }\n        // Check if reCAPTCHA is already loaded\n        if (window.grecaptcha && window.grecaptcha.ready) {\n            setIsLoaded(true);\n            return;\n        }\n        // Load reCAPTCHA script\n        const script = document.createElement(\"script\");\n        script.src = `https://www.google.com/recaptcha/api.js?render=${config.siteKey}`;\n        script.async = true;\n        script.defer = true;\n        script.onload = ()=>{\n            window.grecaptcha.ready(()=>{\n                setIsLoaded(true);\n            });\n        };\n        script.onerror = ()=>{\n            console.error(\"Failed to load reCAPTCHA script\");\n            setIsLoaded(false);\n        };\n        document.head.appendChild(script);\n        return ()=>{\n            // Cleanup script if component unmounts\n            const existingScript = document.querySelector(`script[src*=\"recaptcha\"]`);\n            if (existingScript) {\n                document.head.removeChild(existingScript);\n            }\n        };\n    }, [\n        config.siteKey\n    ]);\n    const executeRecaptcha = async ()=>{\n        if (!isLoaded || !window.grecaptcha) {\n            console.warn(\"reCAPTCHA not loaded yet\");\n            return null;\n        }\n        setIsLoading(true);\n        try {\n            const token = await window.grecaptcha.execute(config.siteKey, {\n                action: config.action\n            });\n            setIsLoading(false);\n            return token;\n        } catch (error) {\n            console.error(\"reCAPTCHA execution failed:\", error);\n            setIsLoading(false);\n            return null;\n        }\n    };\n    return {\n        isLoaded,\n        isLoading,\n        executeRecaptcha\n    };\n};\n// Utility function to get reCAPTCHA site key from environment\nconst getRecaptchaSiteKey = ()=>{\n    const siteKey = \"6LfjK4grAAAAANb-4AFSjCRQX-1gSAHWPFsTKQ9g\";\n    if (!siteKey) {\n        console.warn(\"NEXT_PUBLIC_RECAPTCHA_SITE_KEY not found in environment variables\");\n        return \"\";\n    }\n    return siteKey;\n};\n// Common reCAPTCHA actions for authentication forms only\nconst RECAPTCHA_ACTIONS = {\n    LOGIN: \"login\",\n    REGISTER: \"register\",\n    FORGOT_PASSWORD: \"forgot_password\",\n    OTP_LOGIN: \"otp_login\",\n    OTP_VERIFY: \"otp_verify\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useRecaptcha.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedFetch: () => (/* binding */ authenticatedFetch),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getProfile: () => (/* binding */ getProfile),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   getToken: () => (/* binding */ getToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   removeTokens: () => (/* binding */ removeTokens),\n/* harmony export */   setCurrentUser: () => (/* binding */ setCurrentUser),\n/* harmony export */   setTokens: () => (/* binding */ setTokens),\n/* harmony export */   updateProfile: () => (/* binding */ updateProfile)\n/* harmony export */ });\n// Authentication utilities and API functions\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\n// Token management\nconst getToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n};\nconst getRefreshToken = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"refreshToken\") || sessionStorage.getItem(\"refreshToken\");\n};\nconst setTokens = (token, refreshToken, remember = false)=>{\n    if (true) return;\n    const storage = remember ? localStorage : sessionStorage;\n    storage.setItem(\"token\", token);\n    storage.setItem(\"refreshToken\", refreshToken);\n    // Also store in localStorage for consistency\n    localStorage.setItem(\"token\", token);\n};\nconst removeTokens = ()=>{\n    if (true) return;\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"refreshToken\");\n    localStorage.removeItem(\"user\");\n    sessionStorage.removeItem(\"token\");\n    sessionStorage.removeItem(\"refreshToken\");\n    sessionStorage.removeItem(\"user\");\n};\nconst getCurrentUser = ()=>{\n    if (true) return null;\n    const userStr = localStorage.getItem(\"user\") || sessionStorage.getItem(\"user\");\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch  {\n        return null;\n    }\n};\nconst setCurrentUser = (user)=>{\n    if (true) return;\n    localStorage.setItem(\"user\", JSON.stringify(user));\n};\n// API functions\nconst login = async (data)=>{\n    try {\n        console.log(\"\\uD83D\\uDD10 Login attempt:\", {\n            email: data.email,\n            passwordLength: data.password?.length\n        });\n        console.log(\"\\uD83C\\uDF10 API URL:\", `${API_BASE_URL}/api/auth/login`);\n        const response = await fetch(`${API_BASE_URL}/api/auth/login`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        console.log(\"\\uD83D\\uDCE1 Response status:\", response.status, response.statusText);\n        const result = await response.json();\n        console.log(\"\\uD83D\\uDCCB Response data:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"❌ Login error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst register = async (data)=>{\n    try {\n        const response = await fetch(`${API_BASE_URL}/api/auth/register`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        return await response.json();\n    } catch (error) {\n        console.error(\"Register error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst logout = async ()=>{\n    try {\n        const token = getToken();\n        if (token) {\n            await fetch(`${API_BASE_URL}/api/auth/logout`, {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n        }\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n    } finally{\n        removeTokens();\n    }\n};\nconst getProfile = async ()=>{\n    try {\n        const token = getToken();\n        if (!token) return null;\n        const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        const data = await response.json();\n        if (data.success) {\n            setCurrentUser(data.data.user);\n            return data.data.user;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Get profile error:\", error);\n        return null;\n    }\n};\nconst updateProfile = async (userData)=>{\n    try {\n        const token = getToken();\n        if (!token) {\n            return {\n                success: false,\n                message: \"No authentication token found\"\n            };\n        }\n        console.log(\"Sending profile update request:\", userData);\n        const response = await fetch(`${API_BASE_URL}/api/auth/profile`, {\n            method: \"PUT\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        });\n        const data = await response.json();\n        console.log(\"Profile update response:\", data);\n        if (data.success) {\n            setCurrentUser(data.data.user);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Update profile error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\n// Check if user is authenticated\nconst isAuthenticated = ()=>{\n    return !!getToken();\n};\n// Create authenticated fetch function\nconst authenticatedFetch = async (url, options = {})=>{\n    const token = getToken();\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...options.headers || {}\n    };\n    if (token) {\n        headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n    return fetch(url.startsWith(\"http\") ? url : `${API_BASE_URL}${url}`, {\n        ...options,\n        headers\n    });\n};\n// Refresh token function\nconst refreshAuthToken = async ()=>{\n    try {\n        const refreshToken = getRefreshToken();\n        if (!refreshToken) return false;\n        const response = await fetch(`${API_BASE_URL}/api/auth/refresh`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n        const data = await response.json();\n        if (data.success) {\n            setTokens(data.data.token, data.data.refreshToken);\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error(\"Token refresh error:\", error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1f6b6a191842\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzc1YzEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZjZiNmExOTE4NDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/reset-password/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/auth/reset-password/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\app\auth\reset-password\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/auth/RecaptchaProvider */ \"(rsc)/./src/components/auth/RecaptchaProvider.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"InvoNest - AI-Powered Invoicing & Compliance Platform\",\n    description: \"Secure, AI-powered invoicing and compliance platform for Indian MSMEs, freelancers, and gig workers. GST-compliant invoice generation with blockchain integrity.\",\n    keywords: [\n        \"invoicing\",\n        \"GST\",\n        \"compliance\",\n        \"AI\",\n        \"MSME\",\n        \"India\",\n        \"tax\",\n        \"blockchain\"\n    ],\n    authors: [\n        {\n            name: \"InvoNest Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false,\n    viewportFit: \"cover\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"32x32\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#4f46e5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"InvoNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/icons/icon-192x192.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"application-name\",\n                        content: \"InvoNest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#4f46e5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileImage\",\n                        content: \"/icons/icon-144x144.svg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"format-detection\",\n                        content: \"telephone=no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Cache-Control\",\n                                content: \"no-cache, no-store, must-revalidate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Pragma\",\n                                content: \"no-cache\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                httpEquiv: \"Expires\",\n                                content: \"0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                                name: \"cache-bust\",\n                                content: Date.now().toString()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                                src: \"/disable-turbopack-overlay.js\",\n                                defer: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} font-sans antialiased bg-gray-50 touch-manipulation`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_RecaptchaProvider__WEBPACK_IMPORTED_MODULE_3__.RecaptchaProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-screen safe-area-inset\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.Toaster, {\n                                position: \"top-right\",\n                                toastOptions: {\n                                    duration: 4000,\n                                    style: {\n                                        background: \"#363636\",\n                                        color: \"#fff\"\n                                    },\n                                    success: {\n                                        duration: 3000,\n                                        iconTheme: {\n                                            primary: \"#4ade80\",\n                                            secondary: \"#fff\"\n                                        }\n                                    },\n                                    error: {\n                                        duration: 4000,\n                                        iconTheme: {\n                                            primary: \"#ef4444\",\n                                            secondary: \"#fff\"\n                                        }\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBT01BO0FBTGlCO0FBQ2dDO0FBQ2tCO0FBQy9CO0FBT25DLE1BQU1JLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsVUFBVTtRQUFDO1FBQWE7UUFBTztRQUFjO1FBQU07UUFBUTtRQUFTO1FBQU87S0FBYTtJQUN4RkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBZ0I7S0FBRTtBQUN0QyxFQUFFO0FBRUssTUFBTUMsV0FBVztJQUN0QkMsT0FBTztJQUNQQyxjQUFjO0lBQ2RDLGNBQWM7SUFDZEMsY0FBYztJQUNkQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLOzswQkFDVCw4REFBQ0M7O2tDQUNDLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzt3QkFBZUMsTUFBSzs7Ozs7O2tDQUMxQyw4REFBQ0g7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7d0JBQWVFLE9BQU07Ozs7OztrQ0FDM0MsOERBQUNKO3dCQUFLQyxLQUFJO3dCQUFXQyxNQUFLOzs7Ozs7a0NBQzFCLDhEQUFDRzt3QkFBS2pCLE1BQUs7d0JBQWNrQixTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDRDt3QkFBS2pCLE1BQUs7d0JBQStCa0IsU0FBUTs7Ozs7O2tDQUNsRCw4REFBQ0Q7d0JBQUtqQixNQUFLO3dCQUF3Q2tCLFNBQVE7Ozs7OztrQ0FDM0QsOERBQUNEO3dCQUFLakIsTUFBSzt3QkFBNkJrQixTQUFROzs7Ozs7a0NBQ2hELDhEQUFDTjt3QkFBS0MsS0FBSTt3QkFBbUJDLE1BQUs7Ozs7OztrQ0FDbEMsOERBQUNHO3dCQUFLakIsTUFBSzt3QkFBeUJrQixTQUFROzs7Ozs7a0NBQzVDLDhEQUFDRDt3QkFBS2pCLE1BQUs7d0JBQW1Ca0IsU0FBUTs7Ozs7O2tDQUN0Qyw4REFBQ0Q7d0JBQUtqQixNQUFLO3dCQUEwQmtCLFNBQVE7Ozs7OztrQ0FDN0MsOERBQUNEO3dCQUFLakIsTUFBSzt3QkFBMEJrQixTQUFROzs7Ozs7a0NBQzdDLDhEQUFDRDt3QkFBS2pCLE1BQUs7d0JBQW1Ca0IsU0FBUTs7Ozs7O29CQS9DOUMsS0FnRGtDLGtCQUN4Qjs7MENBQ0UsOERBQUNEO2dDQUFLRSxXQUFVO2dDQUFnQkQsU0FBUTs7Ozs7OzBDQUN4Qyw4REFBQ0Q7Z0NBQUtFLFdBQVU7Z0NBQVNELFNBQVE7Ozs7OzswQ0FDakMsOERBQUNEO2dDQUFLRSxXQUFVO2dDQUFVRCxTQUFROzs7Ozs7MENBQ2xDLDhEQUFDRDtnQ0FBS2pCLE1BQUs7Z0NBQWFrQixTQUFTRSxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7Ozs7OzswQ0FDcEQsOERBQUNDO2dDQUFPQyxLQUFJO2dDQUFnQ0MsS0FBSzs7Ozs7Ozs7Ozs7Ozs7MEJBSXZELDhEQUFDQztnQkFBS0MsV0FBVyxDQUFDLEVBQUVwQyxrTEFBYyxDQUFDLG9EQUFvRCxDQUFDOzBCQUN0Riw0RUFBQ0UsaUZBQWlCQTs4QkFDaEIsNEVBQUNELCtEQUFZQTs7MENBQ1gsOERBQUNxQztnQ0FBSUYsV0FBVTswQ0FDWm5COzs7Ozs7MENBRUwsOERBQUNkLG9EQUFPQTtnQ0FDTm9DLFVBQVM7Z0NBQ1RDLGNBQWM7b0NBQ1pDLFVBQVU7b0NBQ1ZDLE9BQU87d0NBQ0xDLFlBQVk7d0NBQ1pDLE9BQU87b0NBQ1Q7b0NBQ0FDLFNBQVM7d0NBQ1BKLFVBQVU7d0NBQ1ZLLFdBQVc7NENBQ1RDLFNBQVM7NENBQ1RDLFdBQVc7d0NBQ2I7b0NBQ0Y7b0NBQ0FDLE9BQU87d0NBQ0xSLFVBQVU7d0NBQ1ZLLFdBQVc7NENBQ1RDLFNBQVM7NENBQ1RDLFdBQVc7d0NBQ2I7b0NBQ0Y7Z0NBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPWiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zyb250ZW5kLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tIFwiLi4vY29udGV4dHMvQXV0aENvbnRleHRcIjtcbmltcG9ydCB7IFJlY2FwdGNoYVByb3ZpZGVyIH0gZnJvbSBcIi4uL2NvbXBvbmVudHMvYXV0aC9SZWNhcHRjaGFQcm92aWRlclwiO1xuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gXCJyZWFjdC1ob3QtdG9hc3RcIjtcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7XG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxuICB2YXJpYWJsZTogXCItLWZvbnQtaW50ZXJcIixcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJJbnZvTmVzdCAtIEFJLVBvd2VyZWQgSW52b2ljaW5nICYgQ29tcGxpYW5jZSBQbGF0Zm9ybVwiLFxuICBkZXNjcmlwdGlvbjogXCJTZWN1cmUsIEFJLXBvd2VyZWQgaW52b2ljaW5nIGFuZCBjb21wbGlhbmNlIHBsYXRmb3JtIGZvciBJbmRpYW4gTVNNRXMsIGZyZWVsYW5jZXJzLCBhbmQgZ2lnIHdvcmtlcnMuIEdTVC1jb21wbGlhbnQgaW52b2ljZSBnZW5lcmF0aW9uIHdpdGggYmxvY2tjaGFpbiBpbnRlZ3JpdHkuXCIsXG4gIGtleXdvcmRzOiBbXCJpbnZvaWNpbmdcIiwgXCJHU1RcIiwgXCJjb21wbGlhbmNlXCIsIFwiQUlcIiwgXCJNU01FXCIsIFwiSW5kaWFcIiwgXCJ0YXhcIiwgXCJibG9ja2NoYWluXCJdLFxuICBhdXRob3JzOiBbeyBuYW1lOiBcIkludm9OZXN0IFRlYW1cIiB9XSxcbn07XG5cbmV4cG9ydCBjb25zdCB2aWV3cG9ydCA9IHtcbiAgd2lkdGg6ICdkZXZpY2Utd2lkdGgnLFxuICBpbml0aWFsU2NhbGU6IDEsXG4gIG1heGltdW1TY2FsZTogMSxcbiAgdXNlclNjYWxhYmxlOiBmYWxzZSxcbiAgdmlld3BvcnRGaXQ6ICdjb3ZlcicsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGhlYWQ+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uc3ZnXCIgdHlwZT1cImltYWdlL3N2Zyt4bWxcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIHNpemVzPVwiMzJ4MzJcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJtYW5pZmVzdFwiIGhyZWY9XCIvbWFuaWZlc3QuanNvblwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ0aGVtZS1jb2xvclwiIGNvbnRlbnQ9XCIjNGY0NmU1XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLWNhcGFibGVcIiBjb250ZW50PVwieWVzXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImFwcGxlLW1vYmlsZS13ZWItYXBwLXN0YXR1cy1iYXItc3R5bGVcIiBjb250ZW50PVwiZGVmYXVsdFwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJhcHBsZS1tb2JpbGUtd2ViLWFwcC10aXRsZVwiIGNvbnRlbnQ9XCJJbnZvTmVzdFwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImFwcGxlLXRvdWNoLWljb25cIiBocmVmPVwiL2ljb25zL2ljb24tMTkyeDE5Mi5zdmdcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwibW9iaWxlLXdlYi1hcHAtY2FwYWJsZVwiIGNvbnRlbnQ9XCJ5ZXNcIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwiYXBwbGljYXRpb24tbmFtZVwiIGNvbnRlbnQ9XCJJbnZvTmVzdFwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJtc2FwcGxpY2F0aW9uLVRpbGVDb2xvclwiIGNvbnRlbnQ9XCIjNGY0NmU1XCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cIm1zYXBwbGljYXRpb24tVGlsZUltYWdlXCIgY29udGVudD1cIi9pY29ucy9pY29uLTE0NHgxNDQuc3ZnXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImZvcm1hdC1kZXRlY3Rpb25cIiBjb250ZW50PVwidGVsZXBob25lPW5vXCIgLz5cbiAgICAgICAge3Byb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnICYmIChcbiAgICAgICAgICA8PlxuICAgICAgICAgICAgPG1ldGEgaHR0cEVxdWl2PVwiQ2FjaGUtQ29udHJvbFwiIGNvbnRlbnQ9XCJuby1jYWNoZSwgbm8tc3RvcmUsIG11c3QtcmV2YWxpZGF0ZVwiIC8+XG4gICAgICAgICAgICA8bWV0YSBodHRwRXF1aXY9XCJQcmFnbWFcIiBjb250ZW50PVwibm8tY2FjaGVcIiAvPlxuICAgICAgICAgICAgPG1ldGEgaHR0cEVxdWl2PVwiRXhwaXJlc1wiIGNvbnRlbnQ9XCIwXCIgLz5cbiAgICAgICAgICAgIDxtZXRhIG5hbWU9XCJjYWNoZS1idXN0XCIgY29udGVudD17RGF0ZS5ub3coKS50b1N0cmluZygpfSAvPlxuICAgICAgICAgICAgPHNjcmlwdCBzcmM9XCIvZGlzYWJsZS10dXJib3BhY2stb3ZlcmxheS5qc1wiIGRlZmVyIC8+XG4gICAgICAgICAgPC8+XG4gICAgICAgICl9XG4gICAgICA8L2hlYWQ+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2Ake2ludGVyLnZhcmlhYmxlfSBmb250LXNhbnMgYW50aWFsaWFzZWQgYmctZ3JheS01MCB0b3VjaC1tYW5pcHVsYXRpb25gfT5cbiAgICAgICAgPFJlY2FwdGNoYVByb3ZpZGVyPlxuICAgICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBzYWZlLWFyZWEtaW5zZXRcIj5cbiAgICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPFRvYXN0ZXJcbiAgICAgICAgICAgIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCJcbiAgICAgICAgICAgIHRvYXN0T3B0aW9ucz17e1xuICAgICAgICAgICAgICBkdXJhdGlvbjogNDAwMCxcbiAgICAgICAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzM2MzYzNicsXG4gICAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJyxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgc3VjY2Vzczoge1xuICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAzMDAwLFxuICAgICAgICAgICAgICAgIGljb25UaGVtZToge1xuICAgICAgICAgICAgICAgICAgcHJpbWFyeTogJyM0YWRlODAnLFxuICAgICAgICAgICAgICAgICAgc2Vjb25kYXJ5OiAnI2ZmZicsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgZXJyb3I6IHtcbiAgICAgICAgICAgICAgICBkdXJhdGlvbjogNDAwMCxcbiAgICAgICAgICAgICAgICBpY29uVGhlbWU6IHtcbiAgICAgICAgICAgICAgICAgIHByaW1hcnk6ICcjZWY0NDQ0JyxcbiAgICAgICAgICAgICAgICAgIHNlY29uZGFyeTogJyNmZmYnLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICAgIDwvUmVjYXB0Y2hhUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiQXV0aFByb3ZpZGVyIiwiUmVjYXB0Y2hhUHJvdmlkZXIiLCJUb2FzdGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsInZpZXdwb3J0Iiwid2lkdGgiLCJpbml0aWFsU2NhbGUiLCJtYXhpbXVtU2NhbGUiLCJ1c2VyU2NhbGFibGUiLCJ2aWV3cG9ydEZpdCIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiaGVhZCIsImxpbmsiLCJyZWwiLCJocmVmIiwidHlwZSIsInNpemVzIiwibWV0YSIsImNvbnRlbnQiLCJodHRwRXF1aXYiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJzY3JpcHQiLCJzcmMiLCJkZWZlciIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsImRpdiIsInBvc2l0aW9uIiwidG9hc3RPcHRpb25zIiwiZHVyYXRpb24iLCJzdHlsZSIsImJhY2tncm91bmQiLCJjb2xvciIsInN1Y2Nlc3MiLCJpY29uVGhlbWUiLCJwcmltYXJ5Iiwic2Vjb25kYXJ5IiwiZXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/auth/RecaptchaProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/auth/RecaptchaProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RecaptchaBadge: () => (/* binding */ e3),
/* harmony export */   RecaptchaProvider: () => (/* binding */ e0),
/* harmony export */   useRecaptchaContext: () => (/* binding */ e1),
/* harmony export */   withRecaptcha: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\components\auth\RecaptchaProvider.tsx#RecaptchaProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\components\auth\RecaptchaProvider.tsx#useRecaptchaContext`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\components\auth\RecaptchaProvider.tsx#withRecaptcha`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\components\auth\RecaptchaProvider.tsx#RecaptchaBadge`);


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useRequireAuth: () => (/* binding */ e3),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#withAuth`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\invoNest\frontend\src\contexts\AuthContext.tsx#useRequireAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Freset-password%2Fpage&page=%2Fauth%2Freset-password%2Fpage&appPaths=%2Fauth%2Freset-password%2Fpage&pagePath=private-next-app-dir%2Fauth%2Freset-password%2Fpage.tsx&appDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cgupta%5CDesktop%5CinvoNest%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();