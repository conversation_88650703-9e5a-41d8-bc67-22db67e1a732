/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CRecaptchaProvider.tsx%22%2C%22ids%22%3A%5B%22RecaptchaProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CRecaptchaProvider.tsx%22%2C%22ids%22%3A%5B%22RecaptchaProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth/RecaptchaProvider.tsx */ \"(app-pages-browser)/./src/components/auth/RecaptchaProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CRecaptchaProvider.tsx%22%2C%22ids%22%3A%5B%22RecaptchaProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9uYXZpZ2F0aW9uLmpzP2U2NjEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb25cIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={592:(e,r,t)=>{var n=t(722);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},722:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(592);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"a8a2348359be\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NzYzMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE4YTIzNDgzNTliZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth/RecaptchaProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/auth/RecaptchaProvider.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecaptchaBadge: function() { return /* binding */ RecaptchaBadge; },\n/* harmony export */   RecaptchaProvider: function() { return /* binding */ RecaptchaProvider; },\n/* harmony export */   useRecaptchaContext: function() { return /* binding */ useRecaptchaContext; },\n/* harmony export */   withRecaptcha: function() { return /* binding */ withRecaptcha; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useRecaptcha */ \"(app-pages-browser)/./src/hooks/useRecaptcha.ts\");\n/* __next_internal_client_entry_do_not_use__ RecaptchaProvider,useRecaptchaContext,withRecaptcha,RecaptchaBadge auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst RecaptchaContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst RecaptchaProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const siteKey = (0,_hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_2__.getRecaptchaSiteKey)();\n    // Use a default action for initialization, actual action will be passed when executing\n    const { isLoaded, isLoading, executeRecaptcha: baseExecuteRecaptcha } = (0,_hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_2__.useRecaptcha)({\n        siteKey,\n        action: \"init\"\n    });\n    const executeRecaptcha = async (action)=>{\n        if (!isLoaded || !window.grecaptcha) {\n            console.warn(\"reCAPTCHA not loaded yet\");\n            return null;\n        }\n        try {\n            const token = await window.grecaptcha.execute(siteKey, {\n                action\n            });\n            return token;\n        } catch (error) {\n            console.error(\"reCAPTCHA execution failed:\", error);\n            return null;\n        }\n    };\n    const value = {\n        isLoaded,\n        isLoading,\n        executeRecaptcha\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecaptchaContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RecaptchaProvider, \"U7ZhVs7B6wI7MQII7urfJd1OJxw=\", false, function() {\n    return [\n        _hooks_useRecaptcha__WEBPACK_IMPORTED_MODULE_2__.useRecaptcha\n    ];\n});\n_c = RecaptchaProvider;\nconst useRecaptchaContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(RecaptchaContext);\n    if (context === undefined) {\n        throw new Error(\"useRecaptchaContext must be used within a RecaptchaProvider\");\n    }\n    return context;\n};\n_s1(useRecaptchaContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Higher-order component to wrap forms with reCAPTCHA\nconst withRecaptcha = (Component)=>{\n    return (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecaptchaProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n            lineNumber: 68,\n            columnNumber: 5\n        }, undefined);\n};\n// Badge component to show reCAPTCHA branding (required by Google)\nconst RecaptchaBadge = (param)=>{\n    let { className = \"\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-xs text-gray-500 mt-4 \".concat(className),\n        children: [\n            \"This site is protected by reCAPTCHA and the Google\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"https://policies.google.com/privacy\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"text-blue-600 hover:text-blue-800 underline\",\n                children: \"Privacy Policy\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"and\",\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: \"https://policies.google.com/terms\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"text-blue-600 hover:text-blue-800 underline\",\n                children: \"Terms of Service\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            \" \",\n            \"apply.\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\components\\\\auth\\\\RecaptchaProvider.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = RecaptchaBadge;\nvar _c, _c1;\n$RefreshReg$(_c, \"RecaptchaProvider\");\n$RefreshReg$(_c1, \"RecaptchaBadge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/RecaptchaProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useRequireAuth: function() { return /* binding */ useRequireAuth; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth,useRequireAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            try {\n                if ((0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.isAuthenticated)()) {\n                    // Try to get user from localStorage first\n                    const cachedUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getCurrentUser)();\n                    if (cachedUser) {\n                        setUser(cachedUser);\n                    }\n                    // Then refresh from server\n                    const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n                    if (freshUser) {\n                        setUser(freshUser);\n                    } else {\n                        // Token might be expired, clear auth state\n                        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                        setUser(null);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.removeTokens)();\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    const login = async function(data) {\n        let remember = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.login)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, remember);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during login.\"\n            };\n        }\n    };\n    const register = async (data)=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.register)(data);\n            if (response.success && response.data) {\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setTokens)(response.data.token, response.data.refreshToken, true);\n                (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(response.data.user);\n                setUser(response.data.user);\n            }\n            return response;\n        } catch (error) {\n            console.error(\"Register error:\", error);\n            return {\n                success: false,\n                message: \"An unexpected error occurred during registration.\"\n            };\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.logout)();\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            router.push(\"/login\");\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        setUser(updatedUser);\n        (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(updatedUser);\n    };\n    const refreshUser = async ()=>{\n        try {\n            const freshUser = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.getProfile)();\n            if (freshUser) {\n                setUser(freshUser);\n            }\n        } catch (error) {\n            console.error(\"Refresh user error:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        isLoggedIn: !!user,\n        login,\n        register,\n        logout,\n        updateUser,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthProvider, \"J17Kp8z+0ojgAqGoY5o3BCjwWms=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Higher-order component for protected routes\nconst withAuth = (WrappedComponent)=>{\n    var _s = $RefreshSig$();\n    const AuthenticatedComponent = (props)=>{\n        _s();\n        const { isLoggedIn, loading } = useAuth();\n        const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n            if (!loading && !isLoggedIn) {\n                router.push(\"/login\");\n            }\n        }, [\n            isLoggedIn,\n            loading,\n            router\n        ]);\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, undefined);\n        }\n        if (!isLoggedIn) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WrappedComponent, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoNest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n            lineNumber: 195,\n            columnNumber: 12\n        }, undefined);\n    };\n    _s(AuthenticatedComponent, \"IW139HoZJJhS4SfQEBvfy8HldGc=\", false, function() {\n        return [\n            useAuth,\n            next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n        ];\n    });\n    AuthenticatedComponent.displayName = \"withAuth(\".concat(WrappedComponent.displayName || WrappedComponent.name, \")\");\n    return AuthenticatedComponent;\n};\n// Hook for protected routes\nconst useRequireAuth = ()=>{\n    _s2();\n    const { isLoggedIn, loading } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isLoggedIn) {\n            router.push(\"/login\");\n        }\n    }, [\n        isLoggedIn,\n        loading,\n        router\n    ]);\n    return {\n        isLoggedIn,\n        loading\n    };\n};\n_s2(useRequireAuth, \"IW139HoZJJhS4SfQEBvfy8HldGc=\", false, function() {\n    return [\n        useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useRecaptcha.ts":
/*!***********************************!*\
  !*** ./src/hooks/useRecaptcha.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RECAPTCHA_ACTIONS: function() { return /* binding */ RECAPTCHA_ACTIONS; },\n/* harmony export */   getRecaptchaSiteKey: function() { return /* binding */ getRecaptchaSiteKey; },\n/* harmony export */   useRecaptcha: function() { return /* binding */ useRecaptcha; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useRecaptcha,getRecaptchaSiteKey,RECAPTCHA_ACTIONS auto */ \nconst useRecaptcha = (config)=>{\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Check if reCAPTCHA is already loaded\n        if (window.grecaptcha && window.grecaptcha.ready) {\n            setIsLoaded(true);\n            return;\n        }\n        // Load reCAPTCHA script\n        const script = document.createElement(\"script\");\n        script.src = \"https://www.google.com/recaptcha/api.js?render=\".concat(config.siteKey);\n        script.async = true;\n        script.defer = true;\n        script.onload = ()=>{\n            window.grecaptcha.ready(()=>{\n                setIsLoaded(true);\n            });\n        };\n        script.onerror = ()=>{\n            console.error(\"Failed to load reCAPTCHA script\");\n            setIsLoaded(false);\n        };\n        document.head.appendChild(script);\n        return ()=>{\n            // Cleanup script if component unmounts\n            const existingScript = document.querySelector('script[src*=\"recaptcha\"]');\n            if (existingScript) {\n                document.head.removeChild(existingScript);\n            }\n        };\n    }, [\n        config.siteKey\n    ]);\n    const executeRecaptcha = async ()=>{\n        if (!isLoaded || !window.grecaptcha) {\n            console.warn(\"reCAPTCHA not loaded yet\");\n            return null;\n        }\n        setIsLoading(true);\n        try {\n            const token = await window.grecaptcha.execute(config.siteKey, {\n                action: config.action\n            });\n            setIsLoading(false);\n            return token;\n        } catch (error) {\n            console.error(\"reCAPTCHA execution failed:\", error);\n            setIsLoading(false);\n            return null;\n        }\n    };\n    return {\n        isLoaded,\n        isLoading,\n        executeRecaptcha\n    };\n};\n// Utility function to get reCAPTCHA site key from environment\nconst getRecaptchaSiteKey = ()=>{\n    const siteKey = \"6LfjK4grAAAAANb-4AFSjCRQX-1gSAHWPFsTKQ9g\";\n    if (!siteKey) {\n        console.warn(\"NEXT_PUBLIC_RECAPTCHA_SITE_KEY not found in environment variables\");\n        return \"\";\n    }\n    return siteKey;\n};\n// Common reCAPTCHA actions for different forms\nconst RECAPTCHA_ACTIONS = {\n    LOGIN: \"login\",\n    REGISTER: \"register\",\n    FORGOT_PASSWORD: \"forgot_password\",\n    OTP_LOGIN: \"otp_login\",\n    OTP_VERIFY: \"otp_verify\",\n    CONTACT: \"contact\",\n    PAYMENT: \"payment\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VSZWNhcHRjaGEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7d0dBRTRDO0FBYXJDLE1BQU1FLGVBQWUsQ0FBQ0M7SUFDM0IsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdKLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ0ssV0FBV0MsYUFBYSxHQUFHTiwrQ0FBUUEsQ0FBQztJQUUzQ0QsZ0RBQVNBLENBQUM7UUFDUix1Q0FBdUM7UUFDdkMsSUFBSVEsT0FBT0MsVUFBVSxJQUFJRCxPQUFPQyxVQUFVLENBQUNDLEtBQUssRUFBRTtZQUNoREwsWUFBWTtZQUNaO1FBQ0Y7UUFFQSx3QkFBd0I7UUFDeEIsTUFBTU0sU0FBU0MsU0FBU0MsYUFBYSxDQUFDO1FBQ3RDRixPQUFPRyxHQUFHLEdBQUcsa0RBQWlFLE9BQWZYLE9BQU9ZLE9BQU87UUFDN0VKLE9BQU9LLEtBQUssR0FBRztRQUNmTCxPQUFPTSxLQUFLLEdBQUc7UUFFZk4sT0FBT08sTUFBTSxHQUFHO1lBQ2RWLE9BQU9DLFVBQVUsQ0FBQ0MsS0FBSyxDQUFDO2dCQUN0QkwsWUFBWTtZQUNkO1FBQ0Y7UUFFQU0sT0FBT1EsT0FBTyxHQUFHO1lBQ2ZDLFFBQVFDLEtBQUssQ0FBQztZQUNkaEIsWUFBWTtRQUNkO1FBRUFPLFNBQVNVLElBQUksQ0FBQ0MsV0FBVyxDQUFDWjtRQUUxQixPQUFPO1lBQ0wsdUNBQXVDO1lBQ3ZDLE1BQU1hLGlCQUFpQlosU0FBU2EsYUFBYSxDQUFFO1lBQy9DLElBQUlELGdCQUFnQjtnQkFDbEJaLFNBQVNVLElBQUksQ0FBQ0ksV0FBVyxDQUFDRjtZQUM1QjtRQUNGO0lBQ0YsR0FBRztRQUFDckIsT0FBT1ksT0FBTztLQUFDO0lBRW5CLE1BQU1ZLG1CQUFtQjtRQUN2QixJQUFJLENBQUN2QixZQUFZLENBQUNJLE9BQU9DLFVBQVUsRUFBRTtZQUNuQ1csUUFBUVEsSUFBSSxDQUFDO1lBQ2IsT0FBTztRQUNUO1FBRUFyQixhQUFhO1FBRWIsSUFBSTtZQUNGLE1BQU1zQixRQUFRLE1BQU1yQixPQUFPQyxVQUFVLENBQUNxQixPQUFPLENBQUMzQixPQUFPWSxPQUFPLEVBQUU7Z0JBQzVEZ0IsUUFBUTVCLE9BQU80QixNQUFNO1lBQ3ZCO1lBRUF4QixhQUFhO1lBQ2IsT0FBT3NCO1FBQ1QsRUFBRSxPQUFPUixPQUFPO1lBQ2RELFFBQVFDLEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDZCxhQUFhO1lBQ2IsT0FBTztRQUNUO0lBQ0Y7SUFFQSxPQUFPO1FBQ0xIO1FBQ0FFO1FBQ0FxQjtJQUNGO0FBQ0YsRUFBRTtBQUVGLDhEQUE4RDtBQUN2RCxNQUFNSyxzQkFBc0I7SUFDakMsTUFBTWpCLFVBQVVrQiwwQ0FBMEM7SUFDMUQsSUFBSSxDQUFDbEIsU0FBUztRQUNaSyxRQUFRUSxJQUFJLENBQUM7UUFDYixPQUFPO0lBQ1Q7SUFDQSxPQUFPYjtBQUNULEVBQUU7QUFFRiwrQ0FBK0M7QUFDeEMsTUFBTXFCLG9CQUFvQjtJQUMvQkMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLGlCQUFpQjtJQUNqQkMsV0FBVztJQUNYQyxZQUFZO0lBQ1pDLFNBQVM7SUFDVEMsU0FBUztBQUNYLEVBQVciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2hvb2tzL3VzZVJlY2FwdGNoYS50cz80NjBlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxuZGVjbGFyZSBnbG9iYWwge1xuICBpbnRlcmZhY2UgV2luZG93IHtcbiAgICBncmVjYXB0Y2hhOiBhbnk7XG4gIH1cbn1cblxuaW50ZXJmYWNlIFJlY2FwdGNoYUNvbmZpZyB7XG4gIHNpdGVLZXk6IHN0cmluZztcbiAgYWN0aW9uOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjb25zdCB1c2VSZWNhcHRjaGEgPSAoY29uZmlnOiBSZWNhcHRjaGFDb25maWcpID0+IHtcbiAgY29uc3QgW2lzTG9hZGVkLCBzZXRJc0xvYWRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBDaGVjayBpZiByZUNBUFRDSEEgaXMgYWxyZWFkeSBsb2FkZWRcbiAgICBpZiAod2luZG93LmdyZWNhcHRjaGEgJiYgd2luZG93LmdyZWNhcHRjaGEucmVhZHkpIHtcbiAgICAgIHNldElzTG9hZGVkKHRydWUpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIExvYWQgcmVDQVBUQ0hBIHNjcmlwdFxuICAgIGNvbnN0IHNjcmlwdCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3NjcmlwdCcpO1xuICAgIHNjcmlwdC5zcmMgPSBgaHR0cHM6Ly93d3cuZ29vZ2xlLmNvbS9yZWNhcHRjaGEvYXBpLmpzP3JlbmRlcj0ke2NvbmZpZy5zaXRlS2V5fWA7XG4gICAgc2NyaXB0LmFzeW5jID0gdHJ1ZTtcbiAgICBzY3JpcHQuZGVmZXIgPSB0cnVlO1xuXG4gICAgc2NyaXB0Lm9ubG9hZCA9ICgpID0+IHtcbiAgICAgIHdpbmRvdy5ncmVjYXB0Y2hhLnJlYWR5KCgpID0+IHtcbiAgICAgICAgc2V0SXNMb2FkZWQodHJ1ZSk7XG4gICAgICB9KTtcbiAgICB9O1xuXG4gICAgc2NyaXB0Lm9uZXJyb3IgPSAoKSA9PiB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCByZUNBUFRDSEEgc2NyaXB0Jyk7XG4gICAgICBzZXRJc0xvYWRlZChmYWxzZSk7XG4gICAgfTtcblxuICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kQ2hpbGQoc2NyaXB0KTtcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAvLyBDbGVhbnVwIHNjcmlwdCBpZiBjb21wb25lbnQgdW5tb3VudHNcbiAgICAgIGNvbnN0IGV4aXN0aW5nU2NyaXB0ID0gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcihgc2NyaXB0W3NyYyo9XCJyZWNhcHRjaGFcIl1gKTtcbiAgICAgIGlmIChleGlzdGluZ1NjcmlwdCkge1xuICAgICAgICBkb2N1bWVudC5oZWFkLnJlbW92ZUNoaWxkKGV4aXN0aW5nU2NyaXB0KTtcbiAgICAgIH1cbiAgICB9O1xuICB9LCBbY29uZmlnLnNpdGVLZXldKTtcblxuICBjb25zdCBleGVjdXRlUmVjYXB0Y2hhID0gYXN5bmMgKCk6IFByb21pc2U8c3RyaW5nIHwgbnVsbD4gPT4ge1xuICAgIGlmICghaXNMb2FkZWQgfHwgIXdpbmRvdy5ncmVjYXB0Y2hhKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ3JlQ0FQVENIQSBub3QgbG9hZGVkIHlldCcpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRva2VuID0gYXdhaXQgd2luZG93LmdyZWNhcHRjaGEuZXhlY3V0ZShjb25maWcuc2l0ZUtleSwge1xuICAgICAgICBhY3Rpb246IGNvbmZpZy5hY3Rpb24sXG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHJldHVybiB0b2tlbjtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcigncmVDQVBUQ0hBIGV4ZWN1dGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4ge1xuICAgIGlzTG9hZGVkLFxuICAgIGlzTG9hZGluZyxcbiAgICBleGVjdXRlUmVjYXB0Y2hhLFxuICB9O1xufTtcblxuLy8gVXRpbGl0eSBmdW5jdGlvbiB0byBnZXQgcmVDQVBUQ0hBIHNpdGUga2V5IGZyb20gZW52aXJvbm1lbnRcbmV4cG9ydCBjb25zdCBnZXRSZWNhcHRjaGFTaXRlS2V5ID0gKCk6IHN0cmluZyA9PiB7XG4gIGNvbnN0IHNpdGVLZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19SRUNBUFRDSEFfU0lURV9LRVk7XG4gIGlmICghc2l0ZUtleSkge1xuICAgIGNvbnNvbGUud2FybignTkVYVF9QVUJMSUNfUkVDQVBUQ0hBX1NJVEVfS0VZIG5vdCBmb3VuZCBpbiBlbnZpcm9ubWVudCB2YXJpYWJsZXMnKTtcbiAgICByZXR1cm4gJyc7XG4gIH1cbiAgcmV0dXJuIHNpdGVLZXk7XG59O1xuXG4vLyBDb21tb24gcmVDQVBUQ0hBIGFjdGlvbnMgZm9yIGRpZmZlcmVudCBmb3Jtc1xuZXhwb3J0IGNvbnN0IFJFQ0FQVENIQV9BQ1RJT05TID0ge1xuICBMT0dJTjogJ2xvZ2luJyxcbiAgUkVHSVNURVI6ICdyZWdpc3RlcicsXG4gIEZPUkdPVF9QQVNTV09SRDogJ2ZvcmdvdF9wYXNzd29yZCcsXG4gIE9UUF9MT0dJTjogJ290cF9sb2dpbicsXG4gIE9UUF9WRVJJRlk6ICdvdHBfdmVyaWZ5JyxcbiAgQ09OVEFDVDogJ2NvbnRhY3QnLFxuICBQQVlNRU5UOiAncGF5bWVudCcsXG59IGFzIGNvbnN0O1xuXG5leHBvcnQgdHlwZSBSZWNhcHRjaGFBY3Rpb24gPSB0eXBlb2YgUkVDQVBUQ0hBX0FDVElPTlNba2V5b2YgdHlwZW9mIFJFQ0FQVENIQV9BQ1RJT05TXTtcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsInVzZVJlY2FwdGNoYSIsImNvbmZpZyIsImlzTG9hZGVkIiwic2V0SXNMb2FkZWQiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ3aW5kb3ciLCJncmVjYXB0Y2hhIiwicmVhZHkiLCJzY3JpcHQiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJzcmMiLCJzaXRlS2V5IiwiYXN5bmMiLCJkZWZlciIsIm9ubG9hZCIsIm9uZXJyb3IiLCJjb25zb2xlIiwiZXJyb3IiLCJoZWFkIiwiYXBwZW5kQ2hpbGQiLCJleGlzdGluZ1NjcmlwdCIsInF1ZXJ5U2VsZWN0b3IiLCJyZW1vdmVDaGlsZCIsImV4ZWN1dGVSZWNhcHRjaGEiLCJ3YXJuIiwidG9rZW4iLCJleGVjdXRlIiwiYWN0aW9uIiwiZ2V0UmVjYXB0Y2hhU2l0ZUtleSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19SRUNBUFRDSEFfU0lURV9LRVkiLCJSRUNBUFRDSEFfQUNUSU9OUyIsIkxPR0lOIiwiUkVHSVNURVIiLCJGT1JHT1RfUEFTU1dPUkQiLCJPVFBfTE9HSU4iLCJPVFBfVkVSSUZZIiwiQ09OVEFDVCIsIlBBWU1FTlQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRecaptcha.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticatedFetch: function() { return /* binding */ authenticatedFetch; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getProfile: function() { return /* binding */ getProfile; },\n/* harmony export */   getRefreshToken: function() { return /* binding */ getRefreshToken; },\n/* harmony export */   getToken: function() { return /* binding */ getToken; },\n/* harmony export */   isAuthenticated: function() { return /* binding */ isAuthenticated; },\n/* harmony export */   login: function() { return /* binding */ login; },\n/* harmony export */   logout: function() { return /* binding */ logout; },\n/* harmony export */   refreshAuthToken: function() { return /* binding */ refreshAuthToken; },\n/* harmony export */   register: function() { return /* binding */ register; },\n/* harmony export */   removeTokens: function() { return /* binding */ removeTokens; },\n/* harmony export */   setCurrentUser: function() { return /* binding */ setCurrentUser; },\n/* harmony export */   setTokens: function() { return /* binding */ setTokens; },\n/* harmony export */   updateProfile: function() { return /* binding */ updateProfile; }\n/* harmony export */ });\n// Authentication utilities and API functions\nconst API_BASE_URL = \"http://localhost:5000\" || 0;\n// Token management\nconst getToken = ()=>{\n    if (false) {}\n    return localStorage.getItem(\"token\") || sessionStorage.getItem(\"token\");\n};\nconst getRefreshToken = ()=>{\n    if (false) {}\n    return localStorage.getItem(\"refreshToken\") || sessionStorage.getItem(\"refreshToken\");\n};\nconst setTokens = function(token, refreshToken) {\n    let remember = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n    if (false) {}\n    const storage = remember ? localStorage : sessionStorage;\n    storage.setItem(\"token\", token);\n    storage.setItem(\"refreshToken\", refreshToken);\n    // Also store in localStorage for consistency\n    localStorage.setItem(\"token\", token);\n};\nconst removeTokens = ()=>{\n    if (false) {}\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"refreshToken\");\n    localStorage.removeItem(\"user\");\n    sessionStorage.removeItem(\"token\");\n    sessionStorage.removeItem(\"refreshToken\");\n    sessionStorage.removeItem(\"user\");\n};\nconst getCurrentUser = ()=>{\n    if (false) {}\n    const userStr = localStorage.getItem(\"user\") || sessionStorage.getItem(\"user\");\n    if (!userStr) return null;\n    try {\n        return JSON.parse(userStr);\n    } catch (e) {\n        return null;\n    }\n};\nconst setCurrentUser = (user)=>{\n    if (false) {}\n    localStorage.setItem(\"user\", JSON.stringify(user));\n};\n// API functions\nconst login = async (data)=>{\n    try {\n        var _data_password;\n        console.log(\"\\uD83D\\uDD10 Login attempt:\", {\n            email: data.email,\n            passwordLength: (_data_password = data.password) === null || _data_password === void 0 ? void 0 : _data_password.length\n        });\n        console.log(\"\\uD83C\\uDF10 API URL:\", \"\".concat(API_BASE_URL, \"/api/auth/login\"));\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/auth/login\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        console.log(\"\\uD83D\\uDCE1 Response status:\", response.status, response.statusText);\n        const result = await response.json();\n        console.log(\"\\uD83D\\uDCCB Response data:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"❌ Login error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst register = async (data)=>{\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/auth/register\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        return await response.json();\n    } catch (error) {\n        console.error(\"Register error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\nconst logout = async ()=>{\n    try {\n        const token = getToken();\n        if (token) {\n            await fetch(\"\".concat(API_BASE_URL, \"/api/auth/logout\"), {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n        }\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n    } finally{\n        removeTokens();\n    }\n};\nconst getProfile = async ()=>{\n    try {\n        const token = getToken();\n        if (!token) return null;\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/auth/profile\"), {\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        const data = await response.json();\n        if (data.success) {\n            setCurrentUser(data.data.user);\n            return data.data.user;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Get profile error:\", error);\n        return null;\n    }\n};\nconst updateProfile = async (userData)=>{\n    try {\n        const token = getToken();\n        if (!token) {\n            return {\n                success: false,\n                message: \"No authentication token found\"\n            };\n        }\n        console.log(\"Sending profile update request:\", userData);\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/auth/profile\"), {\n            method: \"PUT\",\n            headers: {\n                \"Authorization\": \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(userData)\n        });\n        const data = await response.json();\n        console.log(\"Profile update response:\", data);\n        if (data.success) {\n            setCurrentUser(data.data.user);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Update profile error:\", error);\n        return {\n            success: false,\n            message: \"Network error. Please check your connection and try again.\"\n        };\n    }\n};\n// Check if user is authenticated\nconst isAuthenticated = ()=>{\n    return !!getToken();\n};\n// Create authenticated fetch function\nconst authenticatedFetch = async function(url) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const token = getToken();\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...options.headers || {}\n    };\n    if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    }\n    return fetch(url.startsWith(\"http\") ? url : \"\".concat(API_BASE_URL).concat(url), {\n        ...options,\n        headers\n    });\n};\n// Refresh token function\nconst refreshAuthToken = async ()=>{\n    try {\n        const refreshToken = getRefreshToken();\n        if (!refreshToken) return false;\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/api/auth/refresh\"), {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n        const data = await response.json();\n        if (data.success) {\n            setTokens(data.data.token, data.data.refreshToken);\n            return true;\n        }\n        return false;\n    } catch (error) {\n        console.error(\"Token refresh error:\", error);\n        return false;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"variable":"--font-inter"}],"variableName":"inter"} ***!
  \****************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\",\"variable\":\"__variable_e8ce0c\"};\n    if(true) {\n      // 1752893617815\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdLFwidmFyaWFibGVcIjpcIi0tZm9udC1pbnRlclwifV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsZ0ZBQWdGO0FBQzNHLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUE4SCxjQUFjLHNEQUFzRDtBQUNoTyxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/ZDE5YSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInX19JbnRlcl9lOGNlMGMnLCAnX19JbnRlcl9GYWxsYmFja19lOGNlMGMnXCIsXCJmb250U3R5bGVcIjpcIm5vcm1hbFwifSxcImNsYXNzTmFtZVwiOlwiX19jbGFzc05hbWVfZThjZTBjXCIsXCJ2YXJpYWJsZVwiOlwiX192YXJpYWJsZV9lOGNlMGNcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc1Mjg5MzYxNzgxNVxuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCJDOi9Vc2Vycy9ndXB0YS9EZXNrdG9wL2ludm9OZXN0L2Zyb250ZW5kL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/N2Y3YiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@swc/helpers/esm/_tagged_template_literal.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _: function() { return /* binding */ _tagged_template_literal; },\n/* harmony export */   _tagged_template_literal: function() { return /* binding */ _tagged_template_literal; }\n/* harmony export */ });\nfunction _tagged_template_literal(strings, raw) {\n    if (!raw) raw = strings.slice(0);\n\n    return Object.freeze(Object.defineProperties(strings, { raw: { value: Object.freeze(raw) } }));\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvZXNtL190YWdnZWRfdGVtcGxhdGVfbGl0ZXJhbC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPO0FBQ1A7O0FBRUEsNERBQTRELE9BQU8sNkJBQTZCO0FBQ2hHO0FBQ3lDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvZXNtL190YWdnZWRfdGVtcGxhdGVfbGl0ZXJhbC5qcz81ODRkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBfdGFnZ2VkX3RlbXBsYXRlX2xpdGVyYWwoc3RyaW5ncywgcmF3KSB7XG4gICAgaWYgKCFyYXcpIHJhdyA9IHN0cmluZ3Muc2xpY2UoMCk7XG5cbiAgICByZXR1cm4gT2JqZWN0LmZyZWV6ZShPYmplY3QuZGVmaW5lUHJvcGVydGllcyhzdHJpbmdzLCB7IHJhdzogeyB2YWx1ZTogT2JqZWN0LmZyZWV6ZShyYXcpIH0gfSkpO1xufVxuZXhwb3J0IHsgX3RhZ2dlZF90ZW1wbGF0ZV9saXRlcmFsIGFzIF8gfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/goober/dist/goober.modern.js":
/*!***************************************************!*\
  !*** ./node_modules/goober/dist/goober.modern.js ***!
  \***************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: function() { return /* binding */ u; },\n/* harmony export */   extractCss: function() { return /* binding */ r; },\n/* harmony export */   glob: function() { return /* binding */ b; },\n/* harmony export */   keyframes: function() { return /* binding */ h; },\n/* harmony export */   setup: function() { return /* binding */ m; },\n/* harmony export */   styled: function() { return /* binding */ j; }\n/* harmony export */ });\nlet e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/goober/dist/goober.modern.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/react-hot-toast/dist/index.mjs ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckmarkIcon: function() { return /* binding */ _; },\n/* harmony export */   ErrorIcon: function() { return /* binding */ k; },\n/* harmony export */   LoaderIcon: function() { return /* binding */ V; },\n/* harmony export */   ToastBar: function() { return /* binding */ C; },\n/* harmony export */   ToastIcon: function() { return /* binding */ M; },\n/* harmony export */   Toaster: function() { return /* binding */ Oe; },\n/* harmony export */   \"default\": function() { return /* binding */ Vt; },\n/* harmony export */   resolveValue: function() { return /* binding */ f; },\n/* harmony export */   toast: function() { return /* binding */ c; },\n/* harmony export */   useToaster: function() { return /* binding */ O; },\n/* harmony export */   useToasterStore: function() { return /* binding */ D; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var goober__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! goober */ \"(app-pages-browser)/./node_modules/goober/dist/goober.modern.js\");\n/* __next_internal_client_entry_do_not_use__ CheckmarkIcon,ErrorIcon,LoaderIcon,ToastBar,ToastIcon,Toaster,default,resolveValue,toast,useToaster,useToasterStore auto */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n transform: scale(1) rotate(45deg);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0);\\n  opacity: 0;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(90deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(90deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n\\n  &:after,\\n  &:before {\\n    content: '';\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 150ms;\\n    position: absolute;\\n    border-radius: 3px;\\n    opacity: 0;\\n    background: \",\n        \";\\n    bottom: 9px;\\n    left: 4px;\\n    height: 2px;\\n    width: 12px;\\n  }\\n\\n  &:before {\\n    animation: \",\n        \" 0.15s ease-out forwards;\\n    animation-delay: 180ms;\\n    transform: rotate(90deg);\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject5() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 12px;\\n  height: 12px;\\n  box-sizing: border-box;\\n  border: 2px solid;\\n  border-radius: 100%;\\n  border-color: \",\n        \";\\n  border-right-color: \",\n        \";\\n  animation: \",\n        \" 1s linear infinite;\\n\"\n    ]);\n    _templateObject5 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject6() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0) rotate(45deg);\\n\topacity: 0;\\n}\\nto {\\n  transform: scale(1) rotate(45deg);\\n\topacity: 1;\\n}\"\n    ]);\n    _templateObject6 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject7() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n0% {\\n\theight: 0;\\n\twidth: 0;\\n\topacity: 0;\\n}\\n40% {\\n  height: 0;\\n\twidth: 6px;\\n\topacity: 1;\\n}\\n100% {\\n  opacity: 1;\\n  height: 10px;\\n}\"\n    ]);\n    _templateObject7 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject8() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  width: 20px;\\n  opacity: 0;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: \",\n        \";\\n  position: relative;\\n  transform: rotate(45deg);\\n\\n  animation: \",\n        \" 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n  animation-delay: 100ms;\\n  &:after {\\n    content: '';\\n    box-sizing: border-box;\\n    animation: \",\n        \" 0.2s ease-out forwards;\\n    opacity: 0;\\n    animation-delay: 200ms;\\n    position: absolute;\\n    border-right: 2px solid;\\n    border-bottom: 2px solid;\\n    border-color: \",\n        \";\\n    bottom: 6px;\\n    left: 6px;\\n    height: 10px;\\n    width: 6px;\\n  }\\n\"\n    ]);\n    _templateObject8 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject9() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: absolute;\\n\"\n    ]);\n    _templateObject9 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject10() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-width: 20px;\\n  min-height: 20px;\\n\"\n    ]);\n    _templateObject10 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject11() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\nfrom {\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n}\\nto {\\n  transform: scale(1);\\n  opacity: 1;\\n}\"\n    ]);\n    _templateObject11 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject12() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  position: relative;\\n  transform: scale(0.6);\\n  opacity: 0.4;\\n  min-width: 20px;\\n  animation: \",\n        \" 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\\n    forwards;\\n\"\n    ]);\n    _templateObject12 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject13() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  align-items: center;\\n  background: #fff;\\n  color: #363636;\\n  line-height: 1.3;\\n  will-change: transform;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\\n  max-width: 350px;\\n  pointer-events: auto;\\n  padding: 8px 10px;\\n  border-radius: 8px;\\n\"\n    ]);\n    _templateObject13 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject14() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  display: flex;\\n  justify-content: center;\\n  margin: 4px 10px;\\n  color: inherit;\\n  flex: 1 1 auto;\\n  white-space: pre-line;\\n\"\n    ]);\n    _templateObject14 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject15() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  z-index: 9999;\\n  > * {\\n    pointer-events: auto;\\n  }\\n\"\n    ]);\n    _templateObject15 = function() {\n        return data;\n    };\n    return data;\n}\nvar _s = $RefreshSig$();\nvar W = (e)=>typeof e == \"function\", f = (e, t)=>W(e) ? e(t) : e;\nvar F = (()=>{\n    let e = 0;\n    return ()=>(++e).toString();\n})(), A = (()=>{\n    let e;\n    return ()=>{\n        if (e === void 0 && typeof window < \"u\") {\n            let t = matchMedia(\"(prefers-reduced-motion: reduce)\");\n            e = !t || t.matches;\n        }\n        return e;\n    };\n})();\n\nvar Y = 20;\nvar U = (e, t)=>{\n    switch(t.type){\n        case 0:\n            return {\n                ...e,\n                toasts: [\n                    t.toast,\n                    ...e.toasts\n                ].slice(0, Y)\n            };\n        case 1:\n            return {\n                ...e,\n                toasts: e.toasts.map((o)=>o.id === t.toast.id ? {\n                        ...o,\n                        ...t.toast\n                    } : o)\n            };\n        case 2:\n            let { toast: r } = t;\n            return U(e, {\n                type: e.toasts.find((o)=>o.id === r.id) ? 1 : 0,\n                toast: r\n            });\n        case 3:\n            let { toastId: s } = t;\n            return {\n                ...e,\n                toasts: e.toasts.map((o)=>o.id === s || s === void 0 ? {\n                        ...o,\n                        dismissed: !0,\n                        visible: !1\n                    } : o)\n            };\n        case 4:\n            return t.toastId === void 0 ? {\n                ...e,\n                toasts: []\n            } : {\n                ...e,\n                toasts: e.toasts.filter((o)=>o.id !== t.toastId)\n            };\n        case 5:\n            return {\n                ...e,\n                pausedAt: t.time\n            };\n        case 6:\n            let a = t.time - (e.pausedAt || 0);\n            return {\n                ...e,\n                pausedAt: void 0,\n                toasts: e.toasts.map((o)=>({\n                        ...o,\n                        pauseDuration: o.pauseDuration + a\n                    }))\n            };\n    }\n}, P = [], y = {\n    toasts: [],\n    pausedAt: void 0\n}, u = (e)=>{\n    y = U(y, e), P.forEach((t)=>{\n        t(y);\n    });\n}, q = {\n    blank: 4e3,\n    error: 4e3,\n    success: 2e3,\n    loading: 1 / 0,\n    custom: 4e3\n}, D = function() {\n    let e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    let [t, r] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(y), s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(y);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>(s.current !== y && r(y), P.push(r), ()=>{\n            let o = P.indexOf(r);\n            o > -1 && P.splice(o, 1);\n        }), []);\n    let a = t.toasts.map((o)=>{\n        var n, i, p;\n        return {\n            ...e,\n            ...e[o.type],\n            ...o,\n            removeDelay: o.removeDelay || ((n = e[o.type]) == null ? void 0 : n.removeDelay) || (e == null ? void 0 : e.removeDelay),\n            duration: o.duration || ((i = e[o.type]) == null ? void 0 : i.duration) || (e == null ? void 0 : e.duration) || q[o.type],\n            style: {\n                ...e.style,\n                ...(p = e[o.type]) == null ? void 0 : p.style,\n                ...o.style\n            }\n        };\n    });\n    return {\n        ...t,\n        toasts: a\n    };\n};\nvar J = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"blank\", r = arguments.length > 2 ? arguments[2] : void 0;\n    return {\n        createdAt: Date.now(),\n        visible: !0,\n        dismissed: !1,\n        type: t,\n        ariaProps: {\n            role: \"status\",\n            \"aria-live\": \"polite\"\n        },\n        message: e,\n        pauseDuration: 0,\n        ...r,\n        id: (r == null ? void 0 : r.id) || F()\n    };\n}, x = (e)=>(t, r)=>{\n        let s = J(t, e, r);\n        return u({\n            type: 2,\n            toast: s\n        }), s.id;\n    }, c = (e, t)=>x(\"blank\")(e, t);\nc.error = x(\"error\");\nc.success = x(\"success\");\nc.loading = x(\"loading\");\nc.custom = x(\"custom\");\nc.dismiss = (e)=>{\n    u({\n        type: 3,\n        toastId: e\n    });\n};\nc.remove = (e)=>u({\n        type: 4,\n        toastId: e\n    });\nc.promise = (e, t, r)=>{\n    let s = c.loading(t.loading, {\n        ...r,\n        ...r == null ? void 0 : r.loading\n    });\n    return typeof e == \"function\" && (e = e()), e.then((a)=>{\n        let o = t.success ? f(t.success, a) : void 0;\n        return o ? c.success(o, {\n            id: s,\n            ...r,\n            ...r == null ? void 0 : r.success\n        }) : c.dismiss(s), a;\n    }).catch((a)=>{\n        let o = t.error ? f(t.error, a) : void 0;\n        o ? c.error(o, {\n            id: s,\n            ...r,\n            ...r == null ? void 0 : r.error\n        }) : c.dismiss(s);\n    }), e;\n};\n\nvar K = (e, t)=>{\n    u({\n        type: 1,\n        toast: {\n            id: e,\n            height: t\n        }\n    });\n}, X = ()=>{\n    u({\n        type: 5,\n        time: Date.now()\n    });\n}, b = new Map, Z = 1e3, ee = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : Z;\n    if (b.has(e)) return;\n    let r = setTimeout(()=>{\n        b.delete(e), u({\n            type: 4,\n            toastId: e\n        });\n    }, t);\n    b.set(e, r);\n}, O = (e)=>{\n    let { toasts: t, pausedAt: r } = D(e);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (r) return;\n        let o = Date.now(), n = t.map((i)=>{\n            if (i.duration === 1 / 0) return;\n            let p = (i.duration || 0) + i.pauseDuration - (o - i.createdAt);\n            if (p < 0) {\n                i.visible && c.dismiss(i.id);\n                return;\n            }\n            return setTimeout(()=>c.dismiss(i.id), p);\n        });\n        return ()=>{\n            n.forEach((i)=>i && clearTimeout(i));\n        };\n    }, [\n        t,\n        r\n    ]);\n    let s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        r && u({\n            type: 6,\n            time: Date.now()\n        });\n    }, [\n        r\n    ]), a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((o, n)=>{\n        let { reverseOrder: i = !1, gutter: p = 8, defaultPosition: d } = n || {}, h = t.filter((m)=>(m.position || d) === (o.position || d) && m.height), v = h.findIndex((m)=>m.id === o.id), S = h.filter((m, E)=>E < v && m.visible).length;\n        return h.filter((m)=>m.visible).slice(...i ? [\n            S + 1\n        ] : [\n            0,\n            S\n        ]).reduce((m, E)=>m + (E.height || 0) + p, 0);\n    }, [\n        t\n    ]);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        t.forEach((o)=>{\n            if (o.dismissed) ee(o.id, o.removeDelay);\n            else {\n                let n = b.get(o.id);\n                n && (clearTimeout(n), b.delete(o.id));\n            }\n        });\n    }, [\n        t\n    ]), {\n        toasts: t,\n        handlers: {\n            updateHeight: K,\n            startPause: X,\n            endPause: s,\n            calculateOffset: a\n        }\n    };\n};\n\n\n\n\n\nvar oe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject()), re = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject1()), se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject2()), k = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject3(), (e)=>e.primary || \"#ff4b4b\", oe, re, (e)=>e.secondary || \"#fff\", se);\n\nvar ne = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject4()), V = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject5(), (e)=>e.secondary || \"#e0e0e0\", (e)=>e.primary || \"#616161\", ne);\n\nvar pe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject6()), de = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject7()), _ = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject8(), (e)=>e.primary || \"#61d345\", pe, de, (e)=>e.secondary || \"#fff\");\nvar ue = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject9()), le = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject10()), fe = (0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(_templateObject11()), Te = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject12(), fe), M = (param)=>{\n    let { toast: e } = param;\n    let { icon: t, type: r, iconTheme: s } = e;\n    return t !== void 0 ? typeof t == \"string\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Te, null, t) : t : r === \"blank\" ? null : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(le, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(V, {\n        ...s\n    }), r !== \"loading\" && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ue, null, r === \"error\" ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(k, {\n        ...s\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_, {\n        ...s\n    })));\n};\nvar ye = (e)=>\"\\n0% {transform: translate3d(0,\".concat(e * -200, \"%,0) scale(.6); opacity:.5;}\\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\\n\"), ge = (e)=>\"\\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\\n100% {transform: translate3d(0,\".concat(e * -150, \"%,-1px) scale(.6); opacity:0;}\\n\"), he = \"0%{opacity:0;} 100%{opacity:1;}\", xe = \"0%{opacity:1;} 100%{opacity:0;}\", be = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject13()), Se = (0,goober__WEBPACK_IMPORTED_MODULE_2__.styled)(\"div\")(_templateObject14()), Ae = (e, t)=>{\n    let s = e.includes(\"top\") ? 1 : -1, [a, o] = A() ? [\n        he,\n        xe\n    ] : [\n        ye(s),\n        ge(s)\n    ];\n    return {\n        animation: t ? \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(a), \" 0.35s cubic-bezier(.21,1.02,.73,1) forwards\") : \"\".concat((0,goober__WEBPACK_IMPORTED_MODULE_2__.keyframes)(o), \" 0.4s forwards cubic-bezier(.06,.71,.55,1)\")\n    };\n}, C = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.memo((param)=>{\n    let { toast: e, position: t, style: r, children: s } = param;\n    let a = e.height ? Ae(e.position || t || \"top-center\", e.visible) : {\n        opacity: 0\n    }, o = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(M, {\n        toast: e\n    }), n = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Se, {\n        ...e.ariaProps\n    }, f(e.message, e));\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(be, {\n        className: e.className,\n        style: {\n            ...a,\n            ...r,\n            ...e.style\n        }\n    }, typeof s == \"function\" ? s({\n        icon: o,\n        message: n\n    }) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, o, n));\n});\n\n\n(0,goober__WEBPACK_IMPORTED_MODULE_2__.setup)(react__WEBPACK_IMPORTED_MODULE_1__.createElement);\nvar ve = (param)=>{\n    let { id: e, className: t, style: r, onHeightUpdate: s, children: a } = param;\n    _s();\n    let o = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((n)=>{\n        if (n) {\n            let i = ()=>{\n                let p = n.getBoundingClientRect().height;\n                s(e, p);\n            };\n            i(), new MutationObserver(i).observe(n, {\n                subtree: !0,\n                childList: !0,\n                characterData: !0\n            });\n        }\n    }, [\n        e,\n        s\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: o,\n        className: t,\n        style: r\n    }, a);\n}, Ee = (e, t)=>{\n    let r = e.includes(\"top\"), s = r ? {\n        top: 0\n    } : {\n        bottom: 0\n    }, a = e.includes(\"center\") ? {\n        justifyContent: \"center\"\n    } : e.includes(\"right\") ? {\n        justifyContent: \"flex-end\"\n    } : {};\n    return {\n        left: 0,\n        right: 0,\n        display: \"flex\",\n        position: \"absolute\",\n        transition: A() ? void 0 : \"all 230ms cubic-bezier(.21,1.02,.73,1)\",\n        transform: \"translateY(\".concat(t * (r ? 1 : -1), \"px)\"),\n        ...s,\n        ...a\n    };\n}, De = (0,goober__WEBPACK_IMPORTED_MODULE_2__.css)(_templateObject15()), R = 16, Oe = (param)=>{\n    let { reverseOrder: e, position: t = \"top-center\", toastOptions: r, gutter: s, children: a, containerStyle: o, containerClassName: n } = param;\n    let { toasts: i, handlers: p } = O(r);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        id: \"_rht_toaster\",\n        style: {\n            position: \"fixed\",\n            zIndex: 9999,\n            top: R,\n            left: R,\n            right: R,\n            bottom: R,\n            pointerEvents: \"none\",\n            ...o\n        },\n        className: n,\n        onMouseEnter: p.startPause,\n        onMouseLeave: p.endPause\n    }, i.map((d)=>{\n        let h = d.position || t, v = p.calculateOffset(d, {\n            reverseOrder: e,\n            gutter: s,\n            defaultPosition: t\n        }), S = Ee(h, v);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ve, {\n            id: d.id,\n            key: d.id,\n            onHeightUpdate: p.updateHeight,\n            className: d.visible ? De : \"\",\n            style: S\n        }, d.type === \"custom\" ? f(d.message, d) : a ? a(d) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(C, {\n            toast: d,\n            position: h\n        }));\n    }));\n};\n_s(ve, \"LQ34HCRCKbaP7NB9wB8OQNidTak=\");\nvar Vt = c;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cauth%5C%5CRecaptchaProvider.tsx%22%2C%22ids%22%3A%5B%22RecaptchaProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgupta%5C%5CDesktop%5C%5CinvoNest%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);